<template>
  <div class="px-7 py-4">
    <div class="">
      <div class="">
        <div class="flex justify-between text-lg w-full dark:text-white">
          <div class="">SHINJAGIRA</div>
          <div class="">
            <ul class="flex gap-10 justify-center">
              <li class="">AboutMe</li>
              <li class="">My Projects</li>
              <li class="">Experience</li>
              <li class="">Contact Me</li>
            </ul>
          </div>
          <div class="">English/Kinyarwanda</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
import { ref } from "vue";
import router from "../main";
const routes = ref([]);
const actively = ref(false);
</script>
