{"name": "shin<PERSON><PERSON><PERSON>", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@tailwindcss/vite": "^4.1.8", "primeicons": "^7.0.0", "primevue": "^4.3.5", "vue": "^3.3.11", "vue-router": "^4.2.5"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.4", "autoprefixer": "^10.4.21", "postcss": "^8.5.4", "tailwindcss": "^3.4.17", "vite": "^6.3.5"}}