<template>
  <div class="p-6 bg-white dark:bg-black rounded-2xl shadow-lg border border-gray-200 dark:border-gray-700">
    <div class="flex items-center space-x-4">
      <div>
        <h2 class="text-2xl font-semibold text-gray-900 dark:text-gray-100"><PERSON><PERSON></h2>
        <p class="text-sm text-gray-500 dark:text-gray-400">Software Engineer • Backend & Mobile Dev</p>
      </div>
    </div>

    <p class="mt-4 text-gray-700 dark:text-gray-300 description">
      I Love building scalable microservices and rich mobile experiences. Currently learning C and exploring Kotlin for future projects.
    </p>

    <div class="mt-6 flex justify-between">
      <a
  href="mailto:<EMAIL>"
  class="p-3 bg-black dark:bg-white rounded-full shadow-sm transition-colors duration-200"
>
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      stroke-width="1.5"
      stroke="currentColor"
      class="w-6 h-6 text-white dark:text-black"
    >
      <path
        stroke-linecap="round"
        stroke-linejoin="round"
        d="M21.75 6.75v10.5a2.25 2.25 0 0 1-2.25 2.25h-15a2.25
           2.25 0 0 1-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0 0 19.5
           4.5h-15a2.25 2.25 0 0 0-2.25 2.25m19.5 0v.243a2.25
           2.25 0 0 1-1.07 1.916l-7.5 4.615a2.25 2.25 0 0
           1-2.36 0L3.32 8.91a2.25 2.25 0 0 1-1.07-1.916V6.75"
      />
    </svg>
</a>
      <a
        href="https://instagram.com/arnaud"
        target="_blank"
        class="inline-flex items-center px-4 py-2 bg-pink-500 hover:bg-pink-600 text-white rounded-lg shadow-sm transition-colors duration-200"
      >
        Instagram
      </a>
      <a
        href="https://github.com/arnaud"
        target="_blank"
        class="inline-flex items-center px-4 py-2 bg-gray-800 hover:bg-gray-900 text-white rounded-lg shadow-sm transition-colors duration-200"
      >
      <i class="pi pi-github"></i>

      </a>
    </div>
  </div>
</template>

<script>
export default {
  name: "ProfileCard",
  props: {
    avatar: {
      type: String,
      default: "https://via.placeholder.com/80"
    },
    name: {
      type: String,
      default: "Shema Arnaud"
    },
    title: {
      type: String,
      default: "Software Engineer • Backend & Mobile Dev"
    },
    description: {
      type: String,
      default: "Passionate about building scalable microservices and rich mobile experiences. Currently learning Flutter and exploring Kotlin for future projects."
    },
    contacts: {
      type: Array,
      default: () => ([
        { label: 'Email', url: 'mailto:<EMAIL>', bg: 'bg-blue-500', hover: 'hover:bg-blue-600' },
        { label: 'Instagram', url: 'https://instagram.com/arnaud', bg: 'bg-pink-500', hover: 'hover:bg-pink-600' },
        { label: 'GitHub', url: 'https://github.com/arnaud', bg: 'bg-gray-800', hover: 'hover:bg-gray-900' }
      ])
    }
  }
}
</script>
