import { createApp } from 'vue';
import './style.css';
import App from './App.vue';
import { createRouter, createWebHistory } from 'vue-router';
import Home from './pages/Home.vue';
import Contact from './pages/Contact.vue';
import MyActivities from './pages/Myactivities.vue';
import About from './pages/About.vue';
import Sayhello from './pages/Sayhello.vue'
// import "primevue/resources/themes/saga-blue/theme.css"; 
// import "primevue/resources/primevue.min.css";
import "primeicons/primeicons.css";
import PrimeVue from "primevue/config";

const routes = [
  { path: '/home', component: Home },
  {path:'/',redirect:'/home'},
  { path: '/about', component: About },
  {path:'/sayhello',component:Sayhello},
  { path: '/myActivities', component: MyActivities },
  { path: '/contact', component: Contact }
];

const router = createRouter({
  history: createWebHistory(),
  routes
});

const app = createApp(App);
app.use(router);
app.use(PrimeVue)
app.mount('#app');
export default router;
